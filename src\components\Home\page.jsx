'use client';

import React, { useCallback, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { blogPosts } from '@/data/blogPosts';
import { ArrowRight, Flower2, Sunrise, Heart, MapPin, MessageSquare, BookOpen, Instagram, Facebook, CalendarCheck } from 'lucide-react';

// TOP 1% DESIGN COMPONENTS
import WorldClassProvider from '@/components/WorldClassDesign/WorldClassProvider';
import OpticalTypography from '@/components/WorldClassDesign/OpticalTypography';
import MagneticButton from '@/components/WorldClassDesign/MagneticButton';
import StaggeredReveal from '@/components/WorldClassDesign/StaggeredReveal';
import MicroAnimations from '@/components/WorldClassDesign/MicroAnimations';

const { 
  BreathingAnimation, 
  MagneticHover, 
  SmoothReveal, 
  FloatingAnimation,
  RippleEffect 
} = MicroAnimations;

// SafeIcon component for rendering icons with fallback
const SafeIcon = React.memo(({ Icon, className }) => {
  if (!Icon) return null;
  return <Icon className={className} />;
});
SafeIcon.displayName = 'SafeIcon';

// HeroSection - Ultra Minimalistyczny Design
// Inspirowany: Apple.com, Squarespace, Four Seasons, Aman Hotels
// 
// OPCJE DO WYBORU:
// 1. Overlay: Czarny gradient (aktywny) / Biały overlay / Subtelny czarny
// 2. Przycisk: Zakomentowany dla ultra minimalizmu
// 3. Tekst: Cienka czcionka (font-weight: 200/300), pływający na obrazie
// 4. Obraz: Delikatne filtry (contrast, brightness, opacity)
//
const HeroSection = React.memo(() => {
  const scrollToCombinedSection = useCallback(() => {
    document.getElementById('journey-inspiration')?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center w-full overflow-hidden">
      <div className="absolute inset-0 w-full h-full">
        <div className="relative w-full h-full overflow-hidden">
          <Image
            src="/images/background/bali-hero.webp"
            alt="Bali Hero"
            fill
            priority
            className="object-cover"
            sizes="100vw"
            style={{
              filter: 'contrast(0.9) brightness(1.1)',
              opacity: 0.9
            }}
          />
        </div>
        
        {/* Subtle noise texture for invisible luxury */}
        <div 
          className="absolute inset-0 pointer-events-none opacity-[0.02]"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='1' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
            backgroundSize: '256px 256px',
          }}
        />
      </div>
      
      {/* TOP 1% DESIGN: Optical Typography + Staggered Reveal */}
      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <StaggeredReveal delay={100}>
          <OpticalTypography
            variant="display"
            opticalAlign={true}
            manualKerning={true}
            preventOrphans={true}
            className="text-white mb-6"
            style={{
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
              color: 'white',
            }}
          >
            BALI YOGA JOURNEY
          </OpticalTypography>
          
          <OpticalTypography
            variant="body"
            preventOrphans={true}
            className="text-white max-w-2xl mx-auto mb-12"
            style={{
              opacity: 0.9,
              textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
              color: 'white',
            }}
          >
            Harmonia ducha i tropikalnej przygody
          </OpticalTypography>
          
          {/* Magnetic Button with Ripple Effect */}
          <RippleEffect>
            <MagneticButton
              onClick={scrollToCombinedSection}
              magneticStrength={0.2}
              className="inline-flex items-center px-8 py-4 bg-transparent text-white border border-white/80 text-sm font-light tracking-widest uppercase transition-all duration-300 hover:bg-white/10 hover:border-white group"
              style={{
                borderRadius: '0',
                textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
                backdropFilter: 'blur(1px)',
              }}
              aria-label="Odkryj podróż jogową na Bali"
            >
              <span className="relative z-10">ODKRYJ</span>
            </MagneticButton>
          </RippleEffect>
        </StaggeredReveal>
      </div>
      
      {/* Floating scroll indicator */}
      <FloatingAnimation amplitude={8} duration={3000}>
        <div 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white cursor-pointer opacity-60 hover:opacity-100 transition-opacity duration-300"
          onClick={scrollToCombinedSection}
          style={{
            filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))'
          }}
        >
          <ArrowRight 
            size={24} 
            className="rotate-90" 
          />
        </div>
      </FloatingAnimation>
    </section>
  );
});
HeroSection.displayName = 'HeroSection';

// Card component with TOP 1% design
const Card = React.memo(({ type, title, description, link, icon: Icon, index, imageUrl }) => {
  const isHighlight = type === 'highlight';

  return (
    <SmoothReveal delay={index * 100} offset={30}>
      <MagneticHover strength={0.05}>
        <div className="bg-shell/60 backdrop-blur-md border border-temple/8 shadow-soft hover:shadow-medium transition-all duration-300 flex flex-col h-full overflow-hidden rectangular elegant-border elegant-border-hover group">
          {isHighlight ? (
            <div className="p-6 flex flex-col flex-grow">
              <div className="flex items-center gap-2 mb-3">
                <FloatingAnimation amplitude={4} duration={4000}>
                  <SafeIcon Icon={Icon} className="w-4 h-4 text-temple/70" />
                </FloatingAnimation>
                <OpticalTypography
                  variant="h3"
                  className="text-temple/85 font-light"
                  preventOrphans={true}
                >
                  {title}
                </OpticalTypography>
              </div>
              <OpticalTypography
                variant="body"
                className="text-wood-light/75 text-sm leading-relaxed flex-grow font-light"
                preventOrphans={true}
              >
                {description}
              </OpticalTypography>
            </div>
          ) : (
            <>
              <div className="relative w-full h-[240px] overflow-hidden rectangular">
                <div className="relative w-full h-full">
                  <Image
                    src={imageUrl || '/images/placeholder/image.jpg'}
                    alt={title}
                    fill
                    className="object-cover transition-transform duration-700 group-hover:scale-[1.02]"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  {/* Subtle overlay for depth */}
                  <div className="absolute inset-0 bg-black/[0.02] opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
              </div>
              <div className="p-5 flex flex-col flex-grow">
                <OpticalTypography
                  variant="h3"
                  className="text-temple/85 mb-2 font-light"
                  preventOrphans={true}
                >
                  <Link href={link} className="hover:text-temple/70 transition-colors">
                    {title}
                  </Link>
                </OpticalTypography>
                <OpticalTypography
                  variant="body"
                  className="text-wood-light/75 text-sm leading-relaxed mb-4 flex-grow line-clamp-3 font-light"
                  preventOrphans={true}
                >
                  {description}
                </OpticalTypography>
                <MagneticButton
                  onClick={() => window.location.href = link}
                  magneticStrength={0.1}
                  className="inline-flex items-center text-xs font-light text-temple/70 hover:text-temple transition-colors group mt-auto self-start"
                  aria-label={`Przeczytaj więcej o ${title}`}
                >
                  Czytaj dalej
                  <SafeIcon Icon={ArrowRight} className="ml-1 h-3 w-3 group-hover:translate-x-0.5 transition-transform" />
                </MagneticButton>
              </div>
            </>
          )}
        </div>
      </MagneticHover>
    </SmoothReveal>
  );
});
Card.displayName = 'Card';

const Home = ({ latestPosts }) => {
  const router = useRouter();

  const posts = useMemo(() => latestPosts || blogPosts.slice(0, 3), [latestPosts]);

  const eventData = useMemo(
    () => ({
      highlights: [
        { id: 'ubud', title: 'Ubud', description: 'Duchowe serce Bali, zanurzenie w kulturze i jodze wśród tarasów ryżowych.', icon: Flower2 },
        { id: 'gili-air', title: 'Gili Air', description: 'Rajska wyspa bez samochodów, idealna na relaks i snorkeling.', icon: Sunrise },
        { id: 'uluwatu', title: 'Uluwatu', description: 'Joga na klifach z widokiem na ocean i spektakularne zachody słońca.', icon: Heart },
      ],
    }),
    []
  );

  const combinedItems = useMemo(() => eventData.highlights.map((item) => ({ ...item, type: 'highlight' })), [eventData]);

  const socialLinks = [
    { id: 'instagram', href: 'https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr', label: 'Instagram', icon: Instagram, aria: 'Profil na Instagramie' },
    { id: 'facebook', href: 'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/', label: 'Facebook', icon: Facebook, aria: 'Profil na Facebooku' },
    { id: 'fitssey', href: 'https://app.fitssey.com/Flywithbakasana/frontoffice', label: 'Fitssey (Rezerwacje)', icon: CalendarCheck, aria: 'Profil na Fitssey (rezerwacje)' },
  ];

  const navigateToDetails = useCallback(() => {
    router.push('/opis');
  }, [router]);

  const jsonLdString = useMemo(
    () =>
      JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebPage',
        name: 'Bali Yoga Journey',
        description: 'Harmonia ducha i tropikalnej przygody z Julią Jakubowicz',
        url: 'https://bali-yoga-journey.com',
        mainEntity: {
          '@type': 'Event',
          name: 'Bali Yoga Journey',
          description: 'Harmonia ducha i tropikalnej przygody z Julią Jakubowicz',
          startDate: '2025-06-01T00:00:00Z',
          endDate: '2025-06-10T00:00:00Z',
          location: {
            '@type': 'Place',
            name: 'Bali, Indonezja',
            address: {
              '@type': 'PostalAddress',
              addressLocality: 'Ubud',
              addressCountry: 'ID',
            },
          },
          organizer: {
            '@type': 'Person',
            name: 'Julia Jakubowicz',
            url: 'https://bali-yoga-journey.com/about',
          },
          image: 'https://bali-yoga-journey.com/images/background/bali-hero.webp',
        },
      }),
    []
  );

  return (
    <WorldClassProvider
      config={{
        features: {
          ambientBackground: true,
          smoothScrollIndicator: true,
          progressIndicator: true,
          contextualCursor: true,
          keyboardShortcuts: true,
          performanceMonitoring: true,
          accessibilityEnhancements: true,
        }
      }}
    >
      <div className="relative bg-gradient-to-b from-rice via-sand-light/50 to-ocean/10">
        <HeroSection />
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: jsonLdString }} />

        {/* Sekcja Podróż i Inspiracje - TOP 1% DESIGN */}
        <section id="journey-inspiration" className="professional-spacing bg-gradient-to-b from-rice via-sand-light/50 to-ocean/10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <SmoothReveal delay={0} offset={50}>
              <div className="text-center text-breathing">
                <OpticalTypography
                  variant="h2"
                  opticalAlign={true}
                  preventOrphans={true}
                  className="text-temple tracking-tight mb-6"
                >
                  Podróż i Inspiracje
                </OpticalTypography>
                <OpticalTypography
                  variant="body"
                  preventOrphans={true}
                  className="mt-4 text-wood-light/80 max-w-2xl mx-auto"
                >
                  Odkryj magiczne miejsca Bali, które odwiedzimy podczas naszego retreatu jogowego
                </OpticalTypography>
                <div className="mt-6 w-16 h-px bg-temple/20 mx-auto" />
              </div>
            </SmoothReveal>
            
            <StaggeredReveal delay={75}>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {combinedItems.map((item, index) => (
                  <Card key={item.id} type={item.type} title={item.title} description={item.description} icon={item.icon} index={index} />
                ))}
              </div>
            </StaggeredReveal>
            
            <SmoothReveal delay={200} offset={30}>
              <div className="mt-12 text-center">
                <RippleEffect>
                  <MagneticButton
                    onClick={navigateToDetails}
                    magneticStrength={0.15}
                    className="inline-flex items-center px-6 py-3 bg-temple text-rice text-base font-medium rectangular shadow-soft hover:bg-temple/90 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-temple/50 elegant-border group"
                    aria-label="Poznaj szczegóły programu"
                  >
                    <span className="relative z-10 mr-2">Poznaj szczegóły programu</span>
                    <SafeIcon Icon={ArrowRight} className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                  </MagneticButton>
                </RippleEffect>
              </div>
            </SmoothReveal>
          </div>
        </section>

        {/* Sekcja Opinie - TOP 1% DESIGN */}
        <section className="professional-spacing px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto bg-rice/50 rectangular my-16 elegant-border">
          <SmoothReveal delay={0} offset={50}>
            <div className="text-center text-breathing">
              <OpticalTypography
                variant="h2"
                opticalAlign={true}
                preventOrphans={true}
                className="text-temple tracking-tight mb-6"
              >
                Co mówią uczestnicy
              </OpticalTypography>
              <OpticalTypography
                variant="body"
                preventOrphans={true}
                className="mt-4 text-wood-light/80 max-w-2xl mx-auto"
              >
                Poznaj opinie osób, które doświadczyły naszych retreatów jogowych na Bali
              </OpticalTypography>
              <div className="mt-6 w-16 h-px bg-temple/20 mx-auto" />
            </div>
          </SmoothReveal>
          
          <StaggeredReveal delay={100}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  id: 'marta-k',
                  name: 'Marta K.',
                  text: 'Retreat z Julią to najlepsze, co mogłam dla siebie zrobić. Połączenie jogi, medytacji i eksploracji Bali było idealnie wyważone. Wróciłam odmieniona!',
                  location: 'Warszawa',
                },
                {
                  id: 'tomasz-w',
                  name: 'Tomasz W.',
                  text: 'Jako początkujący w jodze obawiałem się, czy dam radę. Julia stworzyła przestrzeń, w której każdy mógł praktykować na swoim poziomie. Bali zachwyciło mnie!',
                  location: 'Kraków',
                },
                {
                  id: 'karolina-m',
                  name: 'Karolina M.',
                  text: 'Trzeci raz uczestniczę w retreatach Julii i za każdym razem odkrywam coś nowego - zarówno w praktyce jogi, jak i w sobie. Polecam z całego serca!',
                  location: 'Wrocław',
                },
              ].map((testimonial, index) => (
                <MagneticHover key={testimonial.id} strength={0.03}>
                  <div className="bg-rice/95 backdrop-blur-sm p-6 rectangular shadow-soft border border-bamboo/10 flex flex-col elegant-border elegant-border-hover text-breathing group transition-all duration-300">
                    <div className="flex-grow">
                      <OpticalTypography
                        variant="body"
                        preventOrphans={true}
                        className="text-wood-light leading-relaxed mb-4"
                      >
                        "{testimonial.text}"
                      </OpticalTypography>
                    </div>
                    <div className="flex items-center mt-4">
                      <BreathingAnimation intensity={0.01} duration={6000}>
                        <div className="w-10 h-10 rectangular-subtle bg-temple/10 flex items-center justify-center text-temple font-medium">
                          {testimonial.name.charAt(0)}
                        </div>
                      </BreathingAnimation>
                      <div className="ml-3">
                        <OpticalTypography
                          variant="caption"
                          className="text-temple font-medium"
                        >
                          {testimonial.name}
                        </OpticalTypography>
                        <OpticalTypography
                          variant="caption"
                          className="text-wood-light/70"
                        >
                          {testimonial.location}
                        </OpticalTypography>
                      </div>
                    </div>
                  </div>
                </MagneticHover>
              ))}
            </div>
          </StaggeredReveal>
        </section>

        {/* Sekcja Blog - TOP 1% DESIGN */}
        <section className="professional-spacing px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
          <SmoothReveal delay={0} offset={50}>
            <div className="text-center text-breathing">
              <OpticalTypography
                variant="h2"
                opticalAlign={true}
                preventOrphans={true}
                className="text-temple tracking-tight mb-6"
              >
                Z naszego bloga
              </OpticalTypography>
              <OpticalTypography
                variant="body"
                preventOrphans={true}
                className="mt-4 text-wood-light/80 max-w-2xl mx-auto"
              >
                Przeczytaj najnowsze artykuły o jodze, Bali i podróżach
              </OpticalTypography>
              <div className="mt-6 w-16 h-px bg-temple/20 mx-auto" />
            </div>
          </SmoothReveal>
          
          <StaggeredReveal delay={100}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {posts.map((post, index) => (
                <Card
                  key={post.slug}
                  title={post.title}
                  description={post.excerpt}
                  link={`/blog/${post.slug}`}
                  icon={BookOpen}
                  index={index}
                  imageUrl={post.imageUrl}
                />
              ))}
            </div>
          </StaggeredReveal>
          
          <SmoothReveal delay={200} offset={30}>
            <div className="mt-12 text-center">
              <RippleEffect>
                <MagneticButton
                  onClick={() => window.location.href = '/blog'}
                  magneticStrength={0.15}
                  className="inline-flex items-center px-6 py-3 bg-rice text-temple text-base font-medium rectangular shadow-soft border border-temple/10 hover:bg-rice/80 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-temple/20 elegant-border elegant-border-hover group"
                  aria-label="Zobacz wszystkie artykuły"
                >
                  <span className="relative z-10 mr-2">Zobacz wszystkie artykuły</span>
                  <SafeIcon Icon={ArrowRight} className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </MagneticButton>
              </RippleEffect>
            </div>
          </SmoothReveal>
        </section>

        {/* Sekcja Social Media - TOP 1% DESIGN */}
        <section className="professional-spacing px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
          <SmoothReveal delay={0} offset={50}>
            <div className="text-center text-breathing">
              <OpticalTypography
                variant="h2"
                opticalAlign={true}
                preventOrphans={true}
                className="text-temple tracking-tight mb-6"
              >
                Połączmy się
              </OpticalTypography>
              <OpticalTypography
                variant="body"
                preventOrphans={true}
                className="mt-4 text-wood-light/80 max-w-2xl mx-auto"
              >
                Śledź nas w mediach społecznościowych, aby być na bieżąco
              </OpticalTypography>
              <div className="mt-6 w-16 h-px bg-temple/20 mx-auto" />
            </div>
          </SmoothReveal>
          
          <StaggeredReveal delay={100}>
            <div className="flex flex-wrap justify-center gap-8">
              {socialLinks.map((link, index) => (
                <MagneticHover key={link.id} strength={0.1}>
                  <RippleEffect>
                    <a
                      href={link.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label={link.aria}
                      className="flex flex-col items-center p-6 bg-rice/95 backdrop-blur-sm rectangular shadow-soft border border-bamboo/10 hover:shadow-glow transition-all duration-300 elegant-border elegant-border-hover text-breathing group"
                      data-cursor="pointer"
                    >
                      <FloatingAnimation amplitude={6} duration={3000 + index * 500}>
                        <SafeIcon Icon={link.icon} className="w-8 h-8 text-temple mb-3 group-hover:scale-110 transition-transform" />
                      </FloatingAnimation>
                      <OpticalTypography
                        variant="caption"
                        className="text-temple font-medium"
                      >
                        {link.label}
                      </OpticalTypography>
                    </a>
                  </RippleEffect>
                </MagneticHover>
              ))}
            </div>
          </StaggeredReveal>
        </section>

      {/* Sekcja CTA - Profesjonalne odstępy */}
      <section className="professional-spacing px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto mb-16">
        <div className="bg-gradient-to-r from-temple/10 to-ocean/10 rectangular p-12 text-center elegant-border text-breathing">
          <h2 className="text-3xl md:text-4xl font-display text-temple tracking-tight mb-6">
            Gotowa na przygodę życia?
          </h2>
          <p className="text-lg text-wood-light/80 max-w-2xl mx-auto mb-8">
            Dołącz do naszego najbliższego retreatu jogowego na Bali i odkryj harmonię ciała i ducha w otoczeniu tropikalnego raju.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/kontakt"
              className="inline-flex items-center justify-center px-6 py-3 bg-temple text-rice text-base font-medium rectangular shadow-soft hover:bg-temple/90 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-temple/50 elegant-border"
              aria-label="Skontaktuj się z nami"
            >
              Skontaktuj się z nami
              <SafeIcon Icon={MessageSquare} className="ml-2 h-5 w-5" />
            </Link>
            <Link
              href="/program"
              className="inline-flex items-center justify-center px-6 py-3 bg-rice text-temple text-base font-medium rectangular shadow-soft border border-temple/10 hover:bg-rice/80 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-temple/20 elegant-border elegant-border-hover"
              aria-label="Zobacz program"
            >
              Zobacz program
              <SafeIcon Icon={MapPin} className="ml-2 h-5 w-5" />
            </Link>
          </div>
        </div>
      </section>
      </div>
    </WorldClassProvider>
  );
};

export { Home };
